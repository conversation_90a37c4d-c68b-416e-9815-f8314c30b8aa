@import "../static/css/page/orderStopDia.css";
:root {
  --styleColor: #ffff;
  --theme-btn-color: black;
}
[v-cloak] {
  display: none;
}

.layui-layer-content {
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
}
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}
body {
  font-family: "Raleway", sans-serif;
  background: #cccccc;
  line-height: 1.6;
  color: #b8b8b8;
  overflow: hidden;
  /* height: 100vh; */
  position: relative;
  margin: auto auto !important;
}
#app {
  user-select: none;
  background-color: var(--styleColor);
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
a {
  -webkit-transition: 0.3s all ease;
  -o-transition: 0.3s all ease;
  transition: 0.3s all ease;
  color: var(--styleColor);
}
a:hover {
  text-decoration: none;
  color: var(--styleColor);
}
/* loading三个小球 */
#shade {
  display: none;
  background-color: rgba(0, 0, 0, 0);
  position: fixed;
  z-index: 2;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.ftco-cover {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.ftco-cover .menuSelect {
  min-width: 1.4rem;
}
.ftco-cover .menuSelect .selectUl {
  font-size: 0.32rem;
}
.ftco-cover .menuSelect .selectUl a {
  line-height: 0.7rem !important;
}
.ftco-cover .menuSelect .selectUl .changeTxt:hover {
  background-color: var(--styleColor) !important;
}
.ftco-cover .new_inSwitch {
  right: 0.3rem;
  color: #484848;
  position: absolute;
  top: 0.35rem;
  z-index: 5;
  white-space: nowrap;
  border-radius: 0.15rem;
  background-color: #fff;
  font-weight: normal;
  padding: 0.09rem 0.2rem;
  font-size: 0.34rem;
}
.ftco-cover .new_inSwitch span {
  display: block;
  font-weight: normal;
  /* padding: 0.1rem 0.5rem; */
  background-color: #fff;
  border-radius: 0.1rem;
}
#section-home {
  /*lbs mode*/
}
#section-home .footnote_warp {
  position: fixed;
  bottom: 0.2rem;
  left: 0.15rem;
  right: 0.15rem;
  display: flex;
  align-items: flex-end;
  font-size: 12px;
}
#section-home .footnote_warp .byLeftInfo {
  color: #010101;
  text-shadow: 0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8,
    0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8;
  flex: 1;
  margin-right: 0.2rem;
}
#section-home .footnote_warp .byRightInfo {
  padding: 0.1rem 0.15rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.15rem;
  color: #000;
  line-height: 0.42rem;
  /* max-width: 35vw; */
}
.byRightInfo_shopAddr {
  /* overflow-wrap: normal; */
}

.byRightInfo_tip {
  white-space: nowrap;
  /* overflow-wrap: normal; */
}
#section-home .lbs_logout {
  position: absolute;
  top: 0.35rem;
  left: 0.3rem;
  z-index: 5;
}
#section-home .lbs_logout img {
  width: 0.7rem;
}
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #fff;
}
.container > div {
  width: 100%;
  height: 100%;
}
.container .back-img-content {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-color: var(--styleColor);
  /* 覆盖背景底层颜色 */
}

.container .container_center {
  position: absolute;
  width: 100%;
  left: 50%;
  bottom: 31%;
  z-index: 1;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.container .container_center .content_warp {
  position: relative;
  width: 100%;
  min-height: 50px;
}
.container .container_center .content_warp .content_warp_shopAddr {
  text-align: center;
  min-height: 1rem !important;
  padding: 0 4%;
  white-space: normal;
  width: 100%;
  box-sizing: border-box;
  font-size: 0.35rem;
}
.container .container_center .content_warp p {
  margin-top: 0;
  margin-bottom: 0;
  color: #010101;
  /* font-weight: 600; */
  /* text-shadow: 1px 1px black, -1px -1px black, 1px -1px black, -1px 1px black; */
  text-shadow: 0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8,
    0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8, 0 0 2px #faf9f8;
}
.container .container_center .content_warp .content_warp_tableNum_txt {
  font-size: 0.4rem;
  text-align: center;
}
.container .container_center .toHomepageBtn {
  position: relative;
  margin-top: 2rem;
}
.container .container_center .toHomepageBtn .orders_btn {
  background-color: #fff;
  width: 3.87rem;
  height: 0.9rem;
  border: 0.5px solid #fff;
  color: var(--theme-btn-color);
  font-size: 0.39rem;
  outline: none !important;
  font-weight: 500;
  border-radius: 0.15rem;
  position: relative;
}

.container .container_center .toHomepageBtn .orders_btn:active {
  color: white;
  border: 0.5px solid var(--styleColor);
  border-radius: 5px;
  background: var(--styleColor);
  outline: none;
  opacity: 0.2;
}
.container .container_center .toHomepageBtn .disabledBtn {
  pointer-events: none;
  opacity: 0.7;
  cursor: not-allowed;
}

/* 按钮加载状态指示器 */
.btn-loading-indicator {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-spinner {
  width: 0.8rem;
  height: 0.8rem;
  border: 0.08rem solid rgba(255, 255, 255, 0.3);
  border-top: 0.08rem solid var(--theme-btn-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-left: 1rem;
  opacity: 0.8;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.container .container_center .toHomepageBtn #loading {
  display: none;
}
.container .container_center .toHomepageBtn .loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  vertical-align: middle;
  z-index: 501;
}
.container .container_center .toHomepageBtn .loader-3 .dot {
  width: 10px;
  height: 10px;
  background: var(--styleColor);
  border-radius: 50%;
  position: absolute;
  top: calc(50% - 5px);
}
.container .container_center .toHomepageBtn .loader-3 .dot1 {
  left: 0;
  -webkit-animation: dot-jump 0.5s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
  animation: dot-jump 0.5s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
}
.container .container_center .toHomepageBtn .loader-3 .dot2 {
  left: 20px;
  -webkit-animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
  animation: dot-jump 0.5s 0.2s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
}
.container .container_center .toHomepageBtn .loader-3 .dot3 {
  left: 40px;
  -webkit-animation: dot-jump 0.5s 0.4s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
  animation: dot-jump 0.5s 0.4s cubic-bezier(0.77, 0.47, 0.64, 0.28) alternate infinite;
}
@-webkit-keyframes dot-jump {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-15px);
  }
}
@keyframes dot-jump {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-15px);
  }
}
.openTableDia,
.userModelDia {
  font-size: 0.35rem;
  padding: 0.39rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.userModelDia .userModel-form-item,
.openTableDia .userModel-form-item {
  width: 100%;
  display: flex;
  align-content: center;
  justify-content: center;
  font-size: 0.3rem;
}
.userModelDia .userModel-form-item:not(:last-child) {
  margin-bottom: 0.47rem;
}
.userModelDia .userModel-form-item .subBtn {
  padding: 0 2.7rem;
  border-radius: 0.1rem;
}
.openTableDia .userModel-form-item .userModel-label,
.userModelDia .userModel-form-item .userModel-label {
  display: flex;
  /* align-content: center; */
  justify-content: center;
  flex-direction: column;
  margin: 0px !important;
}

.userModelDia .userModel-form-item .passWordIcon,
.userModelDia .userModel-form-item .tableIcon {
  height: 0.59rem;
  padding-right: 0.4rem;
}
.userModelDia .userModel-form-item .userModel-input-block {
  position: relative;
  width: 70%;
}
.userModelDia .userModel-form-item .userModel-input-block .icon-eye-close {
  background: url("../image/invisibleEyeIcon.jpg") no-repeat;
  background-size: 100%;
}
.userModelDia .userModel-form-item .userModel-input-block .icon-eye-open {
  background: url("../image/visibleEyeIcon.jpg") no-repeat;
  background-size: 100%;
}
.userModelDia .userModel-form-item .userModel-input-block .eye-warp {
  position: absolute;
  width: 0.47rem;
  height: 0.47rem;
  top: 50%;
  transform: translateY(-50%);
  right: 0.3rem;
  /* top: 0.7rem; */
}
.checkboxLabel {
  position: relative;
  display: flex;
  margin-bottom: 0;
  align-items: center;
}
.userModelDia .userModel-form-item .checkboxLabel .checkbtn {
  display: inline-block;
  width: 15px;
  height: 15px;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  vertical-align: middle;
}
.checkbtn:after {
  box-sizing: content-box;
  content: "";
  border: 2px solid #fff;
  border-left: 0;
  border-top: 0;
  width: 5px;
  height: 10px;
  position: absolute;
  top: 0;
  left: 5px;
  transform: rotate(45deg) scaleY(0);
  transform-origin: center;
  transition: transform 0.15s ease-in 0.05s;
}
input[type="checkbox"]:checked + span {
  background: #1e9fff;
  border: 1px solid #1e9fff;
}
input[type="checkbox"]:checked + span::after {
  transform: rotate(45deg) scaleY(1);
}
button:focus {
  outline: none;
}
.checkboxTxt {
  font-size: 0.3rem;
  margin-left: 0.15rem;
}
/* 员工模式/普通用户 (弹窗)*/
.openTableLayer,
.userModelLayer {
  width: 85%;
  filter: alpha(opacity=60) !important;
  border: none !important;
  border-radius: 0.1rem !important;
}
.template-two .back-img-content {
  display: flex;
  flex-direction: column;
}
.template-two .back-img-content .top-bcg-warp {
  height: 52%;
}
.template-two .back-img-content .center-info-warp {
  flex: 1;
}
.template-two .back-img-content .bottom-bcg-warp {
  height: 36%;
}
.template-three .back-img-content {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.template-three .back-img-content .center-bcg-warp {
  width: 100%;
  transform: scale(0.8);
  object-fit: cover;
  margin: auto;
}
.template-three .back-img-content .bottom-bcg-warp {
  flex: 1;
}
/** 首页logo图片*/
.logo-warp {
  position: absolute;
  top: 0;
  left: 0;
  /*display: grid;*/
  /*grid-template-columns: repeat(3, 1fr);*/
  /*grid-template-rows: repeat(5, 1fr);*/
  /*grid-gap: 10px;*/
  justify-items: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.logo-warp .logo-img {
  position: absolute;
  max-width: 100%;
}
.openTableDia .userModel-form-item .openTableDiaIcon {
  font-size: 0.5rem;
  padding-right: 0.4rem;
}
/*staff mode 页面*/
.staff-mode-second-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #f4f1ee;
}
.staff-mode-second-page .staff-mode-header {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  position: relative;
  min-height: 13%;
}
.staff-mode-header .staff-header-center {
  font-size: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  box-sizing: border-box;
}
.staff-mode-header .staff-header-center svg {
  margin-top: 0.5rem;
}
.staff-header-center strong {
  margin-top: 0.5rem;
}
.staff-mode-second-page .staff-mode-main {
  width: 100%;
  flex: 1;
  font-size: 20px;
  align-self: center;
  display: grid;
  align-items: center;
}
.staff-mode-second-page .staff-mode-main .staff-mode-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  place-self: center;
  justify-content: space-around;
}
.staff-mode-form .staff-table {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  border-radius: 10px;
  padding: 1rem 0.5rem;
  display: flex;
}
.staff-mode-form .staff-table input {
  flex: 1;
  outline: none;
  border: none;
  caret-color: #c9c9c9;
  padding: 5px;
  background-color: #f4f1ee;
  box-shadow: 0px 0.5px 0px 0px #a8e7ec;
}
.staff-mode-form .staff-table input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px #f4f1ee inset;
  -webkit-text-fill-color: #333;
}
.staff-mode-form .staff-table img {
  width: 0.7rem;
  margin: 0 0.2rem;
  height: 0.9rem;
}
.staff-mode-form input::-webkit-input-placeholder {
  color: #c9c9c9;
  text-decoration-color: #c9c9c9;
  text-overline-color: #c9c9c9;
  font-size: 16px;
}

.staff-mode-main .staff-mode-form .staff-confirm-button {
  border: none;
  outline: none;
  margin-top: 2rem;
  height: 1.3rem;
  width: 60%;
  box-sizing: border-box;
  border-radius: 0.3rem;
  background-color: #f4f1ee;
  text-shadow: #f0f9eb 0.1em 0.1em 0.2em;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
  font-family: sans-serif;
}
.staff-mode-form .staff-confirm-button:active {
  transition: 0.3s;
  opacity: 0.5;
  box-shadow: rgba(14, 30, 37, 0.12) 0px 2px 4px 0px, rgba(14, 30, 37, 0.32) 0px 2px 16px 0px;
}
.staff-confirm-button:active:after {
  transition: 0s;
}
.staff-mode-header .staff-mode-logout {
  width: 1rem;
  position: absolute;
  left: 5%;
  bottom: 0;
  transform: translateY(-85%);
  top: 50%;
  left: 5%;
}
.staff-mode-logout .logoOut {
  width: 100%;
}

.popupThumbnailBox {
  width: 4rem;
  height: 1.4rem;
  display: none;
  position: absolute;
  bottom: -1.6rem;
  left: 50%;
  transform: translateX(-50%);
}
.popupThumbnailImg {
  border-radius: 0.1rem;
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.normalBtn {
  background-color: #1e9fff !important;
  color: #fff !important;
  border: 1px solid #1e9fff !important;
}

@keyframes animated-border {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  100% {
    box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
  }
}

.shadow-spread {
  animation: animated-border 1.5s infinite;
}
.border-flow {
  transition: all 0.3s;
  cursor: pointer;
}

.border-flow:hover {
  filter: contrast(1.1);
}

.border-flow:active {
  filter: contrast(0.9);
}

.border-flow::before,
.border-flow::after {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid #f5f3f2;
  /*border-image: linear-gradient(135deg,#9796f0,#fbc7d4);*/
  border-image-repeat: round;
  border-radius: 10px;
  transition: all 0.5s;
  animation: clippath 3s infinite linear;
}

.border-flow::after {
  animation: clippath 3s infinite -1.5s linear;
}
/* 店铺超时弹窗 */
.timeOutPrompt-Pop {
  text-align: center;
  position: relative;
  line-height: 0.58rem;
  font-size: 0.35rem;
  background-color: rgba(0, 0, 0, 0.7) !important;
  color: #fff;
  border: none;
}
.timeOutPrompt-Pop .layui-layer-content {
  padding: 0.4rem;
  height: unset !important;
}

@keyframes clippath {
  0%,
  100% {
    clip-path: inset(0 0 98% 0);
  }

  25% {
    clip-path: inset(0 98% 0 0);
  }
  50% {
    clip-path: inset(98% 0 0 0);
  }
  75% {
    clip-path: inset(0 0 0 98%);
  }
}
@keyframes circle-in-top-left {
  from {
    clip-path: circle(0%);
  }
  to {
    clip-path: circle(150% at top left);
  }
}

[transition-style="in:circle:top-left"] {
  animation: 2.5s cubic-bezier(0.25, 1, 0.3, 1) circle-in-top-left both;
}

/*----- 选择用餐方式弹窗 start-----*/

.dining-style-modal.modal {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  /*background-color: rgba(0, 0, 0, 0.25);*/
}

.dining-style-modal .modal-container {
  max-height: 85%;
  width: 80%;
  margin-left: auto;
  margin-right: auto;
  background-color: #fff;
  border-radius: 5px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 15px 30px 0 rgba(0, 0, 0, 0.25);
  opacity: 0.8;
}

.modal-container-header {
  padding: 16px 32px;
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-container-title {
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 1;
  font-weight: 700;
  font-size: 1.125;
}
.modal-container-title svg {
  width: 32px;
  height: 32px;
  color: #750550;
}

.modal-container-body {
  /*padding: 24px 32px 30px;*/
  overflow-y: auto;
}

.modal-container-footer {
  padding: 15px 32px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  border-top: 1px solid #ddd;
  gap: 12px;
  position: relative;
}
.modal-container-footer:after {
  content: "";
  display: block;
  position: absolute;
  top: -51px;
  left: 24px;
  right: 24px;
  height: 50px;
  flex-shrink: 0;
  background-image: linear-gradient(to top, rgba(255, 255, 255, 0.75), transparent);
  pointer-events: none;
}

.dining-style-modal .button {
  padding: 12px 20px;
  border-radius: 8px;
  background-color: transparent;
  border: 0;
  font-weight: 600;
  cursor: pointer;
  transition: 0.15s ease;
}
.button.is-ghost:hover,
.button.is-ghost:focus {
  background-color: #dfdad7;
}
.button.is-primary {
  background-color: #750550;
  color: #fff;
  width: 2rem;
}
.button.is-primary:hover,
.button.is-primary:focus {
  background-color: #4a0433;
}

.icon-button {
  padding: 0;
  border: 0;
  background-color: transparent;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  cursor: pointer;
  border-radius: 8px;
  transition: 0.15s ease;
}
.icon-button svg {
  width: 24px;
  height: 24px;
}
.icon-button:hover,
.icon-button:focus {
  background-color: #dfdad7;
}

.dining-style form {
  display: flex;
  font-size: 0.3rem;
}

.dining-style label {
  width: 49.4%;
  display: flex;
  cursor: pointer;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  /*margin: 0 0.2rem 0.375rem 0.2rem;*/
  padding: 24px 32px 30px;
}
.dining-style label.disabled {
  /*color: #ffffff;*/
  background-image: url("../static/img/forbid.jpg");
  background-color: #cccccc;
  background-size: 50%;
  background-repeat: no-repeat;
  background-origin: border-box;
  background-position: center;
}
.dining-style label input {
  position: absolute;
  left: -9999px;
}
.dining-style label .radio-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  min-height: 65px;
}
.dining-style label .radio-wrap img {
  height: 1rem;
  margin-bottom: 0.2rem;
}
.dining-style label .radio-wrap p {
  font-size: 0.33rem;
}
.dining-style label input:checked + span {
  background-color: #d6d6e5;
}
.dining-style label input:checked + span:before {
  box-shadow: inset 0 0 0 0.4375em #00005c;
}
.dining-style label span {
  display: flex;
  align-items: center;
  padding: 0.375em 0.75em 0.375em 0.375em;
  border-radius: 99em;
  transition: 0.25s ease;
}
.dining-style label span:hover {
  background-color: #d6d6e5;
}
.dining-style label span:before {
  display: flex;
  flex-shrink: 0;
  content: "";
  background-color: #fff;
  width: 1.5em;
  height: 1.5em;
  border-radius: 50%;
  margin-right: 0.375em;
  transition: 0.25s ease;
  box-shadow: inset 0 0 0 0.125em #00005c;
}

.dining-style {
  /*padding: 20px 20px 0;*/
}

.dividing-line {
  height: auto;
  border-left: 1px dotted #6c6c6c;
}
/*----- 选择用餐方式弹窗 end-----*/
.member-login-form {
  margin-top: 1.5rem;
}
.user-footer-btn-loggedIn {
  font-size: 0.38rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  background-color: #409eff;
  border-radius: 0.18rem;
  margin: 0 auto;
  color: #fff;
  min-height: 42px;
}
.user-footer-btn-notLoggedIn {
  font-size: 0.3rem;
  display: flex;
  justify-content: center;
  /* align-items: center; */
  /* padding: 0.27rem 0; */
  width: 1.5rem;
  align-items: center;
  background-color: #409eff;
  border-radius: 0.18rem;
  margin: 0 auto;
  color: #fff;
  margin-left: 0.5rem;
}

/* 弹窗媒体样式 */

.popup-video,
.popup-image {
  width: 100% !important;
  height: auto;
  object-fit: contain;
  cursor: pointer;
  display: block;
  max-width: 100%;
  min-height: 100px;
  border-radius: 0.3rem;
}

.popup-link {
  width: 6rem;
  height: 6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.75rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.link-content {
  text-align: center;
  color: #fff;
  z-index: 1;
  position: relative;
}

.link-icon {
  font-size: 1.5rem;
}

/* 背景媒体样式 - 仅模板1 */
.template-one .back-img-content {
  position: relative;
  overflow: hidden;
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: auto;
  object-fit: cover;
  z-index: -1;
  max-width: 100%;
}

/* 模板1 External URL 媒体内容样式 - 按照 .container .back-img-content 方式 */
.template-one .external-url-background {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-color: var(--styleColor);
}

.template-one .external-url-video {
  width: 100% !important;
  height: auto;
  object-fit: cover;
  max-width: 100%;
}

.background-unknown {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  z-index: -1;
}

/* 确保背景容器有正确的定位 */
.back-img-content {
  position: relative;
  overflow: hidden;
}

/* ==================== 广告图弹窗样式 ==================== */

/* 弹窗对话框容器 */
#dialog {
  width: 75%;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 12;
  display: none;
  /* 直接作为flex容器，减少嵌套，实现垂直居中 */
  justify-content: center;
  align-items: center;
}

/* 当弹窗显示时应用flex布局 */
#dialog[style*="block"] {
  display: flex !important;
}

/* 媒体加载指示器 */
.media-loading-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 15;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  animation: fadeIn 0.3s ease-out;
}

/* 加载动画 */
.loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 0.125rem solid rgba(255, 255, 255, 0.3);
  border-top: 0.125rem solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 关闭按钮容器 */
#dialog .close_warp {
  position: absolute;
  top: -1.5rem;
  right: -0.8rem;
  padding: 0.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #fff;
  color: #4f4f4f;
  font-weight: 600;
  font-size: 0.8rem;
}

/* 倒计时显示样式 */
.countdown-display {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.countdown-circle {
  width: 0.8rem;
  height: 0.8rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.08);
  animation: breathe 1.5s ease-in-out infinite;
}

.countdown-text {
  font-size: 0.5rem;
  font-weight: 500;
  line-height: 1;
}

/* 呼吸灯动画效果 */
@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
}

/* 关闭按钮样式 */
.close-button {
  width: 0.8rem;
  height: 0.8rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  color: #666;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.close-button .close-icon {
  font-size: 0.6rem;
  line-height: 0.6;
  font-weight: 400;
  user-select: none;
}

/* 弹窗内容段落样式 */
#dialog p {
  margin: 0 0 12px;
  height: 24px;
  line-height: 24px;
  background: #cccccc;
}

/* 简化媒体容器，直接包含媒体内容和关闭按钮 */
#dialog .maskimg_warp {
  position: relative;
  /* 移除多余的flex布局，因为父容器已经处理了居中 */
}

#dialog .ad_warp {
  border-radius: 0.3rem;
  width: 100%;
  height: auto;
  display: block;
}
/* 兼容小屏幕手机 */
@media only screen and (device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) {
  .container .container_center {
    bottom: 27%;
  }
  #section-home .footnote_warp {
    font-size: 0.3rem;
  }
  .container .container_center .toHomepageBtn {
    margin-top: 0.8rem;
  }
  .popupThumbnailBox {
    height: 1.2rem;
    bottom: -1.5rem;
  }

  /* 弹窗链接样式 */
  .popup-link {
    width: 7.5rem;
    height: 5rem;
    border-radius: 0.5rem;
  }

  #dialog {
    width: 70%;
  }

  /* 小屏幕媒体加载指示器 */
  .media-loading-indicator {
    width: 1.5rem;
    height: 1.5rem;
  }

  .loading-spinner {
    width: 1rem;
    height: 1rem;
    border-width: 0.1rem;
  }
}
