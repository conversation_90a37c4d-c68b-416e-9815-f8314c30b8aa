// 回退刷新
window.addEventListener("pageshow", function (event) {
  //event.persisted属性为true时，表示当前文档是从往返缓存中获取
  if (event.persisted) location.reload()
})
const app = new Vue({
  el: "#app",
  mixins: [accountMixin, globalStoreMixin],
  data: {
    lanObj: [
      { label: "i18nLocaleEn", val: "en" },
      { label: "i18nLocaleZh", val: "zh" }
    ],
    storeNumber: urlResolve().paramsObj && urlResolve().paramsObj.storeNumber,
    initTableNumber: urlResolve().paramsObj && urlResolve().paramsObj.tableNumber,
    localTableNumber: localStorage.getItem("lbsModeTableNumber") || "",
    loginParams: {
      tableNumber: "",
      password: "",
      staffCode: ""
    }, //staff/lbs模式需要自定义输入
    openTableData: {}, //openTable数据
    userModelLayerIndex: "", //弹窗index索引
    openTableStatus: false, //openTable数据是否加载成功
    showLanSection: false, //是否显示语言切换按钮
    resErrorCode: "", //错误code
    currentLan: "", //当前语言
    template: 1, //默认模板
    animatedConfig: {}, //动画配置
    arrLang: {}, //i18n语言模板
    urlParams: urlResolve(), //url参数
    pax: "",
    openTableLayerIndex: "",
    staffDesc: localStorage.getItem("staffDesc") || "",
    advertisingLink: "", //广告跳转链接
    adLinkPThumbnail: "", //广告缩略图跳转链接
    errorOTRequestData: "", //开台接口错误返回的请求数据
    openingInNormalMode: false, //正常模式下未开台填写人数后直接跳转菜单页标识
    initBGImg: false, //是否请求接口加载完成背景图
    EAMandSMModeSecondPage: false, //"EnhAssistMode"和'StaffMode'模式下是否显示手动选择台号页面
    tables: [], //增强助理模式下的桌号列表
    isDisabledBtn: false, //控制开始菜单按钮是否可点击
    initLogoImg: false, //是否存在logo图片&&请求完成
    defaultOss: "https://appwise.oss-cn-hongkong.aliyuncs.com", //默认的图片服务器oss地址
    backupOss: "", //备份的图片服务器oss地址
    jumpTableNum: "", //staffMode或者EnhAssistMode模式下手动填入台号开台
    staffModeUIConfig: {}, //员工模式UI配置
    assistModeUIConfig: {}, //增强助理模式UI配置
    showDiningStyle: false, //是否显示切换堂食外卖模式的弹窗
    takeaway: true, // 店铺的takeaway开关状态
    dineIn: true, // 店铺的堂食开关状态
    initConfigObj: {}, //默认的在openTable接口前请求的UIConfig
    isDiningStyleMode: false, //是否是堂食/外卖自选模式
    memberCenterLogo: "", //会员中心logo
    // 图片加载状态管理
    imageLoadingStates: {
      popup: false,
      background: false,
      logo: false,
      thumbnail: false
    },
    queuedObj: {
      isOrderStop: false,
      isOrderWait: false,
      showTip: "",
      queueTime: 0,
      lastUpdatedTime: 0 //最后更新时间
    }, //订单排队对象
    // Popup Image 媒体状态
    popupMedia: {
      url: "",
      type: "", // 'img', 'video', 'gif', 'unknown'
      hasExternalUrl: false,
      isLoading: false, // 媒体是否正在加载
      videoValidationTimer: null, // 视频有效性检测定时器
      isVideoValid: false // 视频是否有效
    },
    // 弹窗关闭控制
    popupCloseControl: {
      isCloseBtnVisible: false, // 关闭按钮是否可见，初始为false，等媒体加载完成后显示
      countdown: 0, // 倒计时秒数
      countdownTimer: null // 倒计时定时器
    },
    // Background Image 媒体状态
    backgroundMedia: {
      url: "",
      type: "", // 'img', 'video', 'gif', 'unknown'
      hasExternalUrl: false
    },
    isPopupCloseAreaVisible: false // 弹窗关闭区域是否可见（包含关闭按钮和倒计时）
  },
  components: {
    VersionTag,
    "dining-style-dialog": DiningStyleDialog
  },
  methods: {
    // 图片加载状态管理
    updateImageLoadingState(type, isLoading) {
      this.imageLoadingStates[type] = isLoading
      // 检查是否所有关键图片都已加载完成
      this.checkAllImagesLoaded()
    },

    checkAllImagesLoaded() {
      const { popup, background, logo } = this.imageLoadingStates
      // 如果关键图片都加载完成，启用按钮
      if (!popup && !background && !logo) {
        this.isDisabledBtn = false
      }
    },

    // 预加载图片优化方法
    preloadImage(url, timeout = 3000) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        const timer = setTimeout(() => {
          img.onload = img.onerror = null
          reject(new Error('Preload timeout'))
        }, timeout)

        img.onload = () => {
          clearTimeout(timer)
          resolve(img)
        }
        img.onerror = () => {
          clearTimeout(timer)
          reject(new Error('Preload failed'))
        }
        img.src = url
      })
    },

    // menu页面的i18n语言包,用于会员页面
    menuI18nPkg() {
      let { menu, global } = JSON.parse(sessionStorage.getItem("i18nPkg"))
      let pkg = formatI18n([...menu, ...global])
      return pkg[this.currentLan]
    },
    render() {
      return new Promise((resolve, reject) => {
        this.isDisabledBtn = true
        //获取config配置
        this.jsonData(sessionStorage.getItem("oldLanguage")).then(async r => {
          resolve()
          this.checkDevice()
          this.initTemplate()
          if (!this.storeNumber || !this.initTableNumber) {
            layer.msg("link参数错误")
            return
          }

          // 异步加载广告，不阻塞主流程
          this.initPopupAds()
            .catch(err => {
              console.warn("广告图片加载失败:", err)
            })
            .finally(() => {
              // 广告加载完成后启用按钮
              this.isDisabledBtn = false
            })

          //判断模式 - 不等待广告加载完成
          this.checkModel()
        })
      })
    },
    async jsonData(lan) {
      await window.initI18n()
      await getJson("./static/utils/config.json").then(r => {
        document.title = r.BYOD.DocumentTitle || ""
        this.fixLan(lan || r.BYOD.language) // (i.g. 第一语言:en;第二语言:zh;第三语言:thirdLan)
        this.template = r.BYOD.template
        this.animatedConfig = r.BYOD.indexAnimated
      })
    },
    fixLan(lan = "zh") {
      let data = window.i18n[lan]
      for (let key in data) {
        this.$set(this.arrLang, key, data[key])
      }
      this.currentLan = lan
      localStorage.setItem("language", lan) //存入语言(解决刷新页面语言重置问题)
      sessionStorage.setItem("oldLanguage", lan) //存入语言(解决刷新页面语言重置问题)
    },
    //默认在openTable接口前请求UIConfig
    initConfig() {
      return new Promise(async (resolve, reject) => {
        let typeNameList = [
          "browserRestrictions",
          "forceSalesMode",
          "pcBackgroundColor",
          "address",
          "salesControl"
        ]
        const res = await this.getSomeUIConfig(typeNameList)
        if (!res.data.length) return
        typeNameList.forEach(e => {
          let config = res.data.find(el => el.type === e)
          if (config) {
            let value = config.value
            if (e == "forceSalesMode") {
              this.initConfigObj[e] = value == "FB" ? 2 : 1
            } else {
              try {
                this.initConfigObj[e] = JSON.parse(value)
              } catch (error) {
                this.initConfigObj[e] = value
              }
            }
          }
        })
        //执行相关回调
        this.setConfigCallBack()
        resolve()
      })
    },
    initSomePay() {
      let {
        webBaseUrl,
        performType,
        payType: { eft = [] } = {},
        displayCRM: { showInfo = [] } = {}
      } = this.openTableData
      let isPay = performType == 2 && eft.length != 0 && showInfo.includes("Recharge")
      // openTable获取配置前缀pix字段,动态加载script标签支付依赖
      if (webBaseUrl && !window.SpiralPG && isPay) {
        //动态创建script标签,前缀+"spiralpg.min.js",async属性加载
        let script = document.createElement("script")
        script.type = "text/javascript"
        script.src = webBaseUrl + "spiralpg.min.js"
        script.async = true
        document.body.appendChild(script)
      }
    },
    // 当获取到选择的用餐方式<外卖/堂食>
    onGetDiningCallBack(res) {
      if (res === "TAKEAWAY" && !this.takeaway) {
        layer.msg(this.arrLang.takeawayClosePrompt)
        return false
      }
      if (res !== "TAKEAWAY" && !this.dineIn) {
        layer.msg(this.arrLang.dineInTimeoutPrompts)
        return false
      }
      this.initTableNumber = res
      this.checkModel(false)
      this.showDiningStyle = false
    },
    //  检查是否弹出选择就餐方式的弹窗
    async checkDiningStyle() {
      try {
        //获取diningStyle配置
        const res = await this.getSomeUIConfig(["diningStyle"])
        let diningConfig = res.data.find(e => e.type === "diningStyle")
        this.takeaway = res.storeTakeawayUsable || false
        this.dineIn = res.storeUsable || false
        // 若存在配置,且不包含当前tableNumber,则需要弹出
        if (diningConfig) {
          let exclude = diningConfig.value.split(";").filter(e => e) || []
          if (exclude.includes(this.initTableNumber)) {
            this.$nextTick(() => {
              this.showDiningStyle = true
              this.isDiningStyleMode = true //是否是堂食/外卖自选模式
            })
          } else {
            throw new Error("在排除列表中,请求openTable")
          }
        } else {
          throw new Error("不存在diningStyle配置")
        }
      } catch (e) {
        this.checkModel(false)
      }
    },
    //检查tableNumber 模式 ,
    checkModel(type = true) {
      let EAMandSMAutoLogin = sessionStorage.getItem("EAMandSMAutoLogin") // 不存在自动登录再弹窗密码框
      switch (this.initTableNumber) {
        case "AssistMode": {
          this.showUserModelDia()
          layer.title(this.arrLang.openTableDiaTitle, this.openTableLayerIndex) //重置弹窗标题
          break
        }
        case "EnhAssistMode": {
          if (!EAMandSMAutoLogin) {
            this.showUserModelDia()
            layer.title(this.arrLang.enhAssistModeLoginTitle, this.userModelLayerIndex) //重置弹窗标题
          }
          break
        }
        case "lbsMode": {
          //缓存存在tableNumber,则不弹窗,可以直接请求openTable
          if (this.localTableNumber) {
            // this.loginParams.tableNumber = this.localTableNumber
          } else {
            this.showUserModelDia()
          }
          break
        }
        case "StaffMode": {
          if (!EAMandSMAutoLogin) {
            this.showUserModelDia()
          }
          break
        }
        case "PREORDER": {
          this.openTable(this.initTableNumber)
          break
        }
        case "TAKEAWAY": {
          this.openTable(this.initTableNumber)
          break
        }
        default: {
          if (type) {
            this.checkDiningStyle().catch()
          } else {
            this.openTable(this.initTableNumber)
          }
          break
        }
      }
    },
    //openTable interface
    openTable(tableNumber, staffPassword = "", pax = 0) {
      if (this.queuedObj.isOrderStop) return //超出订单限制禁止开台
      let oTableLoading = layer.load(2)
      this.openTableStatus = false
      let performType = this.getPerformType()
      let { companyName, paramsObj, urlPrefix } = this.urlParams
      let language = this.currentLan || localStorage.getItem("language")
      sessionStorage.setItem("domain", companyName)
      sessionStorage.setItem("storeNumber", this.storeNumber)
      sessionStorage.setItem("data", paramsObj["data"] || "")
      sessionStorage.setItem("versionNumber", paramsObj["versionNumber"] || "") //存入版本号,用于根据版本对应显示数据(有可能是cms历史版本预览跳转;''=正式;0=UAT;时间戳=历史版本)
      let requestData = {
        companyName,
        storeNumber: this.storeNumber,
        tableNumber: tableNumber,
        urlData: paramsObj["data"] || "",
        staffPassword,
        pax,
        language,
        performType
      }

      if (!(pax > 0)) delete requestData.pax //无人数删除字段
      if (!["AssistMode", "EnhAssistMode"].includes(this.initTableNumber)) {
        delete requestData.staffPassword //非员工模式删除字段
      } else {
        requestData.mode = this.initTableNumber
      }
      if (this.initTableNumber === "lbsMode") requestData.lbs = true
      if (this.initTableNumber === "StaffMode") {
        let { staffCode } = this.loginParams
        requestData = {
          ...requestData,
          mode: "StaffMode",
          staffPassword,
          staffCode
        }
      }
      sendOpenTableRequest(requestData, "index")
        .then(result => {
          result = JSON.parse(result)
          //处理错误
          const code = result.statusCode
          if (!result || (code && code !== 200)) {
            this.errorOTRequestData = result
            this.opTableRequestTip(code)
            return
          }
          if (result && !result.errorCode) {
            this.openTableStatus = true
            const parseConfig = dynamicConfig && dynamicConfig(result.uiConfigList)
            let allResult = {
              ...result,
              language,
              companyName,
              storeNumber: this.storeNumber,
              tableNumber: tableNumber,
              initialTableNum: this.initTableNumber,
              urlPrefix, //url前缀(支付传参使用)
              performType: result.performType || performType, //优先使用返回的performType
              versionNumber: paramsObj["versionNumber"] || "", //版本号
              isDiningStyleMode: this.isDiningStyleMode, //是否是堂食/外卖自选模式
              ...parseConfig
            }
            delete allResult.uiConfigList
            console.log(JSON.parse(JSON.stringify(allResult)), "openTableData")
            this.openTableData = allResult
            this.opTableSucStyle(allResult) //成功之后修改样式和文字及添加第三语言
            this.resErrorCode = "" // 成功后重置错误码
            //判断模式以及保存密码或者删除密码,是否需要走删存密码逻辑;
            let { needSetPassword } = this.checkPageAndSetPassword()
            if (needSetPassword) this.setStaffPassword(this.initTableNumber, staffPassword) //切记不可放在opTableSucCheckModel之后,否则无法获取dom判断是否是第一页登录
            this.$nextTick(this.opTableSucCheckModel.bind(this, this.initTableNumber, allResult)) //检查模式
            this.initSomePay() //初始化支付(充值使用)
          } else if (result.errorCode) {
            this.opTableRequestTip(401) //提示层
          }
        })
        .catch(err => {
          console.log("🚀 ~ file: index.js:190 ~ openTable ~ err:", err)
          this.opTableRequestTip(401)
        })
        .finally(() => {
          layer.close(oTableLoading) //關閉加載層
        })
    },

    //错误提示 openTable
    opTableRequestTip(code) {
      let {
        staffModelErrorTip,
        setTableNumError,
        errorDesc,
        noTableKeyError,
        staffModeErrorText,
        errorTxt,
        errorTableKey,
        staticQRCodeNoOpenTableError
      } = this.arrLang
      let useTip = null
      this.resErrorCode = code
      // let { errorDesc: errorRes } = this.errorOTRequestData
      switch (code) {
        case 406:
          useTip = staffModelErrorTip
          break
        case 408:
          useTip = noTableKeyError
          break
        case 409:
          this.isShowOpenTableDia()
          return
          break
        case 410: //无此台号
          useTip = setTableNumError
          break
        case 416:
          useTip = staffModeErrorText
          this.loginParams.tableNumber = ""
          this.loginParams.staffCode = ""
          break
        case 5003:
          useTip = errorTableKey //tableKey不正确
          break
        case 5005:
          //staticQRCodeNoOpenTableError字符串里面替换#tableNumber为当前桌号
          useTip = staticQRCodeNoOpenTableError.replace("#tableNumber", this.initTableNumber) //未开台
          break
        case 4006: //外卖/堂食;店铺超时
        case 4011: //外卖/堂食;店铺关闭
          return timeOutPromptPop("", "index")
          break
        case 6005:
          useTip = errorDesc
          break
        default:
          useTip = errorTxt
          break
      }
      layer.msg(useTip, { time: 2000, shade: [0.01, "#fff"] }, () => {
        if (["AssistMode", "EnhAssistMode", "StaffMode"].includes(this.initTableNumber)) {
          if (this.EAMandSMModeSecondPage) {
            this.modeLogout()
          } else {
            this.showUserModelDia()
          }
        }
      }) //提示层
      // this.resErrorCode = code
      if (this.initTableNumber === "lbsMode" && !this.localTableNumber) {
        this.resErrorCode = "" // 重置错误状态码避免点击按钮进入goNextPageBtn逻辑的错误提示
      }
    },
    //弹窗组件 staff\lbs
    async showUserModelDia() {
      await this.setStaffAssModeSession()
      let { staffModelLoginTitle } = this.arrLang
      layer.open({
        skin: "userModelLayer",
        type: 1,
        // shade: [0.1, '#fff'],
        shade: 0,
        closeBtn: 0,
        title: staffModelLoginTitle,
        content: $(".userModelDia"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
        success: (layero, index) => {
          let staffPassword = Cookies.get("staffPassword")
          if (staffPassword) {
            $("#remPassWord").prop("checked", true)
            this.loginParams.password = staffPassword
          } else {
            // jq true或者false如 checked, selected 或者 disabled 使用prop()，其他的使用 attr()
            $("#remPassWord").prop("checked", false)
          }
          this.isDisabledBtn = true
          this.userModelLayerIndex = index
        },
        cancel: (index, layero) => {
          this.loginParams.tableNumber = ""
          this.loginParams.password = ""
          $("#passWordDom").prop("type", "password")
          $("#passwordeye").removeClass("icon-eye-open").addClass("icon-eye-close") //密码不可见
          // this.onPreOrderCancel()
        },
        end: params => {
          // this.tableInput = ""
          // this.loginParams.password = ""
          this.isDisabledBtn = false
        }
      })
    },
    // 显示开台弹窗
    showOpenTableDia() {
      let { openTableDiaTitle, OTDSubBtn, OTDCancelBtn, invalidPaxTip } = this.arrLang
      let normalMode = this.openingInNormalMode //409状态码后判断是否是正常模式
      const closeFun = () => {
        // 不存在409状态下的fixQRAutoOpenTable模式(消费者未开台自己开台功能)
        //目前AssistMode模式下才二次弹窗
        if (this.initTableNumber == "AssistMode") this.showUserModelDia()
      }
      layer.open({
        skin: "openTableLayer",
        type: 1,
        // shade: [0.1, '#fff'],
        // shade: 0,
        closeBtn: normalMode ? 0 : 1, //正常模式下填写人数不可取消
        btn: [OTDCancelBtn, OTDSubBtn],
        title: openTableDiaTitle,
        content: $(".openTableDia"), //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相
        success: (layero, index) => {
          if (normalMode) {
            //填写人数禁用取消按钮
            $(".openTableLayer .layui-layer-btn0").addClass("layui-btn-disabled")
            $(".openTableLayer .layui-layer-btn1").addClass("normalBtn")
          }
          this.openTableLayerIndex = index
        },
        yes: (index, layero) => {
          if (normalMode) return false //手动达到禁用效果
          // 取消
          layer.close(index)
          //staffMode 取消不显示弹窗
          closeFun()
        },
        btn2: (index, layero) => {
          // 提交人数
          if (this.pax == "") {
            layer.msg(invalidPaxTip)
            return false
          } else {
            let tableNumber,
              staffPassword = ""
            if (normalMode) {
              // 正常模式获取解析的台号
              let { paramsObj } = this.urlParams
              tableNumber = paramsObj.tableNumber
            } else {
              // 特殊模式获取填写的账号密码
              tableNumber = this.loginParams.tableNumber
              staffPassword = this.loginParams.password
            }
            this.openTable(tableNumber, staffPassword, this.pax)
          }
        },
        cancel: () => {
          closeFun()
        },
        end: () => {
          this.pax = ""
        }
      })
    },
    /**
     * @description:区分409未开台是否是正常模式,判断是否弹出开台提示,是否默认提交人数
     * @param mode:"fixQRAutoOpenTable",代表正常模式下开启了不需要弹窗并且没有默认人数
     */
    isShowOpenTableDia() {
      let { mode } = this.errorOTRequestData
      let { noOpenTableKError, isOpenTableBtn1, isOpenTableBtn2 } = this.arrLang
      if (mode == "fixQRAutoOpenTable") {
        this.openingInNormalMode = true
        // 判断是否有广告图配置,有则广告图关闭再弹窗,无则直接弹窗
        let hasPopupImageItem = $(".ad_warp").attr("src")
        if (hasPopupImageItem == "") this.showOpenTableDia()
      } else {
        layer.confirm(
          noOpenTableKError,
          {
            closeBtn: 0,
            title: false,
            btn: [isOpenTableBtn1, isOpenTableBtn2] //按钮
          },
          (index, layero) => {
            // 否
            layer.close(index)
            // 只有AssistMode模式下才重新弹出登录弹窗
            if (this.initTableNumber == "AssistMode") this.showUserModelDia()
          },
          (index, layero) => {
            // 是
            layer.close(index)
            this.showOpenTableDia()
          }
        )
      }
    },
    //点击弹窗 stafflbs 提交按钮
    onAssistModeSub() {
      let tableNumber = this.loginParams.tableNumber
      let staffPassword = this.loginParams.password
      let { tableRequiredTip } = this.arrLang
      // 公用组件下必填台号的模式AssistMode"
      if (!tableNumber && ["AssistMode"].includes(this.initTableNumber)) {
        layer.msg(tableRequiredTip, {
          shade: [0],
          time: 1000,
          anim: 6
        })
      } else {
        layer.closeAll()
        switch (this.initTableNumber) {
          case "lbsMode":
            this.requestCheckPassword(tableNumber, staffPassword)
            break
          case "AssistMode":
            let pax = Number(sessionStorage.getItem("assistModePax"))
            this.openTable(tableNumber, staffPassword, pax)
            break
          case "EnhAssistMode":
          case "StaffMode":
            // 显示table-grid-container show()
            this.openTable("", staffPassword)
            break
          default:
            this.openTable(tableNumber, staffPassword)
            break
        }
      }
    },
    //验证 lbs 密码
    requestCheckPassword(tableNumber, staffPassword) {
      let { companyName } = this.urlParams
      let data = {
        companyName,
        storeNumber: this.storeNumber,
        mode: "lbs",
        password: staffPassword
      }
      $.ajax({
        type: "post",
        url: "./store/checkPassword",
        data,
        success: res => {
          res = JSON.parse(res)
          let code = res.statusCode
          if (code !== 200) {
            this.opTableRequestTip(code)
            this.resErrorCode = ""
            setTimeout(this.showUserModelDia, 2000)
          } else {
            localStorage.setItem("lbsModeTableNumber", tableNumber)
            this.localTableNumber = tableNumber
          }
        },
        error: err => {
          this.opTableRequestTip(401)
        }
      })
    },
    //显示隐藏对应的switchPwd()方法:
    onPassWordEye() {
      let dom = $("#passWordDom")
      let type = dom.attr("type")
      if (type === "text") {
        dom.prop("type", "password")
        $("#passwordeye").removeClass("icon-eye-open").addClass("icon-eye-close") //密码不可见
      } else {
        dom.prop("type", "text")
        $("#passwordeye").removeClass("icon-eye-close").addClass("icon-eye-open") //密码可见
      }
    },
    //初始化背景图片
    initBcg() {
      if (!this.urlParams) return
      let { companyName, paramsObj } = this.urlParams
      console.log(companyName, paramsObj)
      let data = {
        domain: companyName,
        storeNumber: paramsObj["storeNumber"],
        typeNameList: [
          "Background Image",
          "Company Logo",
          "PC Version Background Image",
          "Member Center Image"
        ]
      }
      let params = {
        url: "./photoConfig/getSpecifiedPhotoConfig",
        dataType: "json",
        traditional: true,
        type: "post",
        data,
        // xhrFields: {
        //     responseType: 'json',
        // },
        success: res => {
          let selector = [
            [".back-img-content"],
            [".top-bcg-warp", ".bottom-bcg-warp"],
            [".top-bcg-warp", ".center-bcg-warp", ".bottom-bcg-warp"]
          ]
          if (res.ossBackupUrl) {
            //   判断是否为规范的oss地址(http://...)
            let reg = /^http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?$/
            if (reg.test(res.ossBackupUrl)) {
              this.backupOss = res.ossBackupUrl
            } else {
              // 加上https://
              this.backupOss = `https://${res.ossBackupUrl}`
            }
            sessionStorage.setItem("backupOssUrl", this.backupOss)
          }
          if (res.photoConfigList.length) {
            res.photoConfigList.forEach(el => {
              const nameArr = el.fileName && el.fileName.split(";")
              if (el.externalUrl && el.typeName === "Background Image") {
                // 处理Background Image的externalUrl
                detectType(el.externalUrl)
                  .then(mediaType => {
                    // 设置 Vue 状态
                    this.backgroundMedia = {
                      url: el.externalUrl,
                      type: mediaType,
                      hasExternalUrl: true
                    }
                  })
                  .catch(e => {
                    console.error("Background Image媒体类型检测失败:", e)
                  })
              } else if (nameArr.length === this.template && el.typeName === "Background Image") {
                let k = selector[this.template - 1]
                k.forEach((o, i) => {
                  let url = this.imagePath("back", { ...el, name: nameArr[i] })
                  checkImage(url, [this.defaultOss, this.backupOss], 3000)
                    .then(r => {
                      let { url: checkUrl, ratio } = r
                      if (this.template === 3) {
                        //模板3 存在定位position,需要设置宽高
                        let targetDOM = document.querySelector(o)
                        // 获取可视宽度
                        let clientWidth = document.documentElement.clientWidth
                        targetDOM.style.height = parseInt(clientWidth / ratio) + "px"
                        console.log(targetDOM.style.height, clientWidth / ratio)
                      }
                      $(o).css({ "background-image": `url('${checkUrl}')` })
                    })
                    .catch(e => {
                      console.warn("背景图片加载失败:", url, e)
                    })
                })
              } else if (el.typeName === "Company Logo") {
                let url = this.imagePath("logo", el)
                checkImage(url, [this.defaultOss, this.backupOss], 3000)
                  .then(r => {
                    this.initLogoImg = true
                    this.$nextTick(() => {
                      // 兼容旧数据,给定默认位置,左上角
                      let coordinate =
                        typeof el.coordinate === "number" || !el.coordinate
                          ? { left: "0%", top: "0%", width: "30%" }
                          : JSON.parse(el.coordinate)
                      $(".logo-img")
                        .attr("src", r.url)
                        .css({ ...coordinate })
                        .addClass("animate__fadeInTopLeft")
                    })
                  })
                  .catch(e => {
                    console.warn("Logo图片加载失败:", url, e)
                  })
              } else if (el.typeName === "PC Version Background Image") {
                if (!this.isPcDevices) return
                // 保存pc背景图
                sessionStorage.setItem("PCbgImg", JSON.stringify(el))
                let url = this.imagePath("pc", el)
                checkImage(url, [this.defaultOss, this.backupOss], 3000)
                  .then(r => {
                    //html设置背景图
                    $("html").css({
                      "background-image": `url('${r.url}')`,
                      "background-size": "cover"
                    })
                  })
                  .catch(e => {
                    console.warn("PC背景图片加载失败:", url, e)
                  })
              } else if (el.typeName === "Member Center Image") {
                let url = this.imagePath("logo", el)
                checkImage(url, [this.defaultOss, this.backupOss], 3000)
                  .then(({ url }) => {
                    this.memberCenterLogo = encodeURI(url)
                  })
                  .catch(e => {
                    console.warn("会员中心图片加载失败:", url, e)
                  })
              }
            })
          }
        },
        error: err => {
          this.opTableRequestTip(401)
        },
        complete: () => {
          setTimeout(() => {
            this.initBGImg = true
          }, 200)
        }
      }
      $.ajax(params)
    },
    getArea(coo) {
      const rows = 5 // 网格的行数
      const cols = 3 // 网格的列数
      const row = Math.ceil(coo / cols) // 计算 x 所在的行
      const col = ((coo - 1) % cols) + 1 // 计算 x 所在的列
      return { row, col }
    },
    //初始化模板显示
    initTemplate() {
      this.resetDOMPosition()
      this.initBcg()
    },
    //初始化弹窗广告
    initPopupAds() {
      return new Promise((resolve, reject) => {
        let specialMode = ["lbsMode", "AssistMode", "StaffMode", "EnhAssistMode"]
        if (specialMode.includes(this.initTableNumber)) {
          resolve()
          return
        }
        // if (this.initTableNumber === "lbsMode" && !this.localTableNumber) return
        // if (this.initTableNumber === "AssistMode") return // 员工模式禁用广告图
        // if (this.initTableNumber === "StaffMode") return
        let { companyName } = this.urlParams
        let data = {
          domain: companyName,
          storeNumber: this.storeNumber,
          typeNameList: ["Popup Image", "Popup Thumbnail"]
        }
        $.ajax({
          url: "./photoConfig/getSpecifiedPhotoConfig",
          type: "post",
          dataType: "json",
          traditional: true,
          data,
          // xhrFields: {
          //     responseType: 'json',
          // },
          success: res => {
            if (!this.backupOss && res.ossBackupUrl) {
              //   判断是否为规范的oss地址(http://...)
              let reg = /^http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?$/
              if (reg.test(res.ossBackupUrl)) {
                this.backupOss = res.ossBackupUrl
              } else {
                // 加上https://
                this.backupOss = `https://${res.ossBackupUrl}`
              }
              sessionStorage.setItem("backupOssUrl", this.backupOss)
            }
            let photoConfigList = res.photoConfigList
            if (photoConfigList && photoConfigList.length != 0) {
              // let item = res.photoConfigList[0]
              let popupImageItem = photoConfigList.find(e => {
                return e.typeName == "Popup Image"
              })

              let popupThumbnailItem = photoConfigList.find(e => {
                return e.typeName == "Popup Thumbnail"
              })
              // 判断是否存在广告缩略图
              const checkHasPopupThumbnail = () => {
                if (popupThumbnailItem) {
                  this.adLinkPThumbnail = popupThumbnailItem.url // 广告缩略图跳转链接
                  let popupThumbnailUrl = this.imagePath("pop", popupThumbnailItem)
                  checkImage(popupThumbnailUrl, [this.defaultOss, this.backupOss], 2000)
                    .then(r => {
                      $(".popupThumbnailImg").attr("src", r.url)
                      // 判断是否存在广告缩略图url
                      $(".popupThumbnailBox").show() // 落单按钮下面的广告推广缩略图
                    })
                    .catch(e => {
                      console.warn("缩略图加载失败:", popupThumbnailUrl, e)
                    })
                }
              }

              if (popupImageItem) {
                this.advertisingLink = popupImageItem.url // 广告图跳转链接

                // 检查是否有externalUrl
                if (popupImageItem.externalUrl) {
                  // 使用linkTypeDetector检测媒体类型
                  detectType(popupImageItem.externalUrl)
                    .then(mediaType => {
                      this.popupMedia = {
                        url: popupImageItem.externalUrl,
                        type: mediaType,
                        hasExternalUrl: true,
                        videoValidationTimer: null,
                        isVideoValid: false
                      }

                      // 如果是视频类型，启动5秒定时器检测视频有效性
                      if (mediaType === "video") {
                        this.startVideoValidationTimer()
                      }

                      // 显示弹窗
                      this.showPopupDialog(popupImageItem, checkHasPopupThumbnail)
                    })
                    .finally(() => {
                      resolve()
                    })
                } else {
                  // 原来的逻辑 - 使用优化后的checkImage，设置较短的超时时间
                  let popupImageUrl = this.imagePath("pop", popupImageItem)
                  checkImage(popupImageUrl, [this.defaultOss, this.backupOss], 2000)
                    .then(r => {
                      $(".ad_warp").attr("src", r.url)
                      this.showPopupDialog(popupImageItem, checkHasPopupThumbnail)
                    })
                    .catch(err => {
                      console.warn("弹窗广告图片加载失败:", err)
                      // 即使图片加载失败，也要检查缩略图
                      checkHasPopupThumbnail()
                    })
                    .finally(() => {
                      resolve() // 无论解析是否成功,都需放行请求openTable
                    })
                }
              } else {
                resolve()
                checkHasPopupThumbnail()
              }
            } else {
              resolve() // 无数据直接放行
            }
            this.isDisabledBtn = false
          },
          error: err => {
            //TODO mockJS please delete
            layer.msg("Ad image request failed") //提示层
            this.isDisabledBtn = false
            reject()
          }
        })
      })
    },
    // 显示弹窗对话框的公共方法
    showPopupDialog(popupImageItem, checkHasPopupThumbnail) {
      // 设置媒体加载状态
      this.popupMedia.isLoading = true
      this.popupCloseControl.countdown = popupImageItem.closeableAfterDelay || 0
      this.popupCloseControl.isCloseBtnVisible = false

      $("#fullbg").show()
      $("#dialog").show({
        duration: 200,
        complete: () => {
          checkHasPopupThumbnail()
        }
      })
    },

    // 媒体加载完成事件
    onMediaLoaded() {
      // console.log("媒体加载完成")
      // 设置媒体加载完成状态
      this.popupMedia.isLoading = false

      // 显示关闭区域
      this.isPopupCloseAreaVisible = true
      // console.log("this.popupCloseControl.countdown", this.popupCloseControl)
      // 触发延迟关闭功能，确保倒计时在媒体加载完成后开始
      if (this.popupCloseControl.countdown > 0) {
        this.handlePopupCloseDelay(this.popupCloseControl.countdown)
      } else {
        // 没有倒计时延迟，媒体加载完成后直接显示关闭按钮
        this.popupCloseControl.isCloseBtnVisible = true
      }
    },

    // 图片媒体加载失败事件处理
    onMediaError(event) {
      if (this.popupMedia.type === "video") return
      // 将媒体类型设置为未知类型
      this.popupMedia.type = "unknown"
      this.popupMedia.isLoading = false
      // 显示关闭区域（未知类型链接也需要显示关闭按钮）
      this.isPopupCloseAreaVisible = true
      this.popupCloseControl.isCloseBtnVisible = true
    },
    onVideoCanPlay(event) {
      // 清除视频有效性检测定时器
      if (this.popupMedia.videoValidationTimer) {
        clearTimeout(this.popupMedia.videoValidationTimer)
        this.popupMedia.videoValidationTimer = null
      }

      // 标记视频为有效
      this.popupMedia.isVideoValid = true
    },

    // 启动视频有效性检测定时器
    startVideoValidationTimer() {
      // 清除之前的定时器
      if (this.popupMedia.videoValidationTimer) {
        clearTimeout(this.popupMedia.videoValidationTimer)
        this.popupMedia.videoValidationTimer = null
      }

      // 重置视频有效状态
      this.popupMedia.isVideoValid = false

      // 启动5秒定时器
      this.popupMedia.videoValidationTimer = setTimeout(() => {
        // 5秒后检查视频是否有效
        if (!this.popupMedia.isVideoValid) {
          this.handleVideoValidationFailure()
        }
      }, 5000)
    },

    // 处理视频验证失败
    handleVideoValidationFailure() {
      // 将媒体类型设置为未知类型
      this.popupMedia.type = "unknown"
      this.popupMedia.isLoading = false

      // 显示关闭区域（未知类型链接也需要显示关闭按钮）
      this.isPopupCloseAreaVisible = true
      this.popupCloseControl.isCloseBtnVisible = true
    },

    // 处理弹窗延迟关闭功能
    handlePopupCloseDelay(closeableAfterDelay) {
      // 清除之前的定时器
      if (this.popupCloseControl.countdownTimer) {
        clearInterval(this.popupCloseControl.countdownTimer)
        this.popupCloseControl.countdownTimer = null
      }

      // 如果有延迟时间且大于0
      if (closeableAfterDelay && closeableAfterDelay > 0) {
        this.popupCloseControl.isCloseBtnVisible = false
        this.popupCloseControl.countdown = closeableAfterDelay

        // 开始倒计时
        this.popupCloseControl.countdownTimer = setInterval(() => {
          this.popupCloseControl.countdown--

          if (this.popupCloseControl.countdown <= 0) {
            // 倒计时结束，显示关闭按钮
            this.popupCloseControl.isCloseBtnVisible = true
            this.popupCloseControl.countdown = 0
            clearInterval(this.popupCloseControl.countdownTimer)
            this.popupCloseControl.countdownTimer = null
          }
        }, 1000)
      } else {
        // 没有延迟，但不立即显示关闭按钮，等待媒体加载完成后再显示
        this.popupCloseControl.isCloseBtnVisible = false
        this.popupCloseControl.countdown = 0
        // 注意：关闭按钮的显示将在 onMediaLoaded 方法中处理
      }
    },
    //拼接图片路径
    imagePath(type, params) {
      let { domain, storeNumber, typeName, extraPaths, fileName, name } = params
      let useBackupOss = sessionStorage.getItem("useBackupOss")
      let defaultUrl = this.defaultOss
      let backupUrl = this.backupOss
      let baseUrl = `${
        !!useBackupOss ? backupUrl || defaultUrl : defaultUrl
      }/${domain}/${storeNumber}/image/${typeName}/`
      if (type === "back") {
        let imgTemplate = extraPaths || fileName.split(";").length + "/"
        baseUrl += `${imgTemplate}${name}.jpg?${new Date().getTime()}`
      } else if (type === "nNT") {
        //nNT = notNeedTimestamp 不需要时间戳
        baseUrl += `${fileName}.jpg`
      } else {
        baseUrl += `${fileName}.jpg?${new Date().getTime()}`
      }
      return baseUrl
    },
    //openTable success change style
    opTableSucStyle(result_value) {
      $("html").css({ "--styleColor": result_value.color, "--theme-btn-color": result_value.color })
      if (result_value.thirdLan && !this.lanObj.find(el => el.val === "thirdLan")) {
        this.lanObj.push({ label: result_value.thirdLan, val: "thirdLan" })
      }
      this.showLanSection = result_value.allowSwitchLanguage
    },
    //下单前校验是否已经登录
    inviteLoginDia() {
      return new Promise((resolve, reject) => {
        const { yes, no, PCLoginConfirmationMessage } = this.menuI18nPkg()
        layer.confirm(
          PCLoginConfirmationMessage,
          {
            title: false,
            closeBtn: false,
            skin: "baseLayer payAtCashierLayer",
            area: ["85%"],
            btn: [no, yes]
          },
          (index, layero) => {
            layer.close(index)
            reject(false)
          },
          () => {
            this.showUserPopup().then(() => {
              resolve(true)
            })
          }
        )
      })
    },
    // 显示个人中心
    showUserPopup() {
      return new Promise((res, rej) => {
        layer.open({
          skin: "userLayer layui-custom-style",
          type: 1,
          title: false, //不显示标题
          closeBtn: 0,
          shadeClose: false,
          area: ["100%", "100%"],
          content: $("#personalCenterPopup"),
          success: (layero, index) => {
            this.$refs.personalCenter.setUserPopIndex(index)
            //判断cookie是否存在userInfo,存在则直接显示用户信息
            this.$refs.personalCenter.setUserInfo()
            let personalCenter = app.$root.$refs.personalCenter
            personalCenter.authFormGetLabelWidth()
            // this.$refs.personalCenter.handleKeyboardShow() //添加监听
          },
          end: () => {
            this.$refs.personalCenter.resetLoginForm()
            // this.$refs.personalCenter.removeEventListener() //移除监听
            res(true)
          }
        })
      })
    },
    // 校验会员邀请登录逻辑
    async checkInviteLogin() {
      let status =
        this.openTableData.displayCRM && this.openTableData.displayCRM.inviteMemberToLogin
      if (!status) return Promise.resolve().catch()
      let disableMode = ["StaffMode", "EnhAssistMode", "lbsMode", "AssistMode"]
      if (disableMode.includes(this.tableNumber)) return Promise.resolve().catch()
      if (this.LoginValid()) {
        // 验证会员
        let domain = sessionStorage.getItem("domain")
        let storeNumber = this.storeNumber
        let tableNumber = this.tableNumber
        return this.$refs.personalCenter
          .loginMemberApi({ domain, storeNumber, tableNumber }, true)
          .then(r => {
            // 后端cookie设置userLogged状态
          })
          .catch(_ => {
            // 无效,后端cookie删除userLogged状态,直接跳转至menu
          })
      } else {
        return this.inviteLoginDia()
          .then(r => {
            if (this.LoginValid()) {
            } else {
              sessionStorage.setItem("notInviteLogin", true)
            }
          })
          .catch(e => {
            // 有notInviteLogin时,在menu不在邀请登录
            sessionStorage.setItem("notInviteLogin", true)
          })
      }
    },
    // 开台成功后检查模式
    opTableSucCheckModel(tableNumber, allResult) {
      // 新增openingInNormalMode条件:正常模式未开台填写人数成功后直接跳转
      //isDiningStyleMode自定义堂食/外卖模式openTable成功后直接跳转
      if (
        ["lbsMode", "AssistMode"].includes(tableNumber) ||
        this.openingInNormalMode ||
        this.isDiningStyleMode
      ) {
        this.goNextPageBtn()
      } else if (tableNumber === "EnhAssistMode" || tableNumber === "StaffMode") {
        let hasTableKey = allResult.tableKey // 返回的数据有tableKey代表开台成功进入下一页(可作为第二页标识判断)
        if (this.EAMandSMModeSecondPage && hasTableKey) {
          layer.load(2) // 持续loading,解决开台成功后,页面跳转前loading消失的问题
          this.goNextPageBtn()
        } else {
          this.tables = allResult.tableList || []
          this.showEAMandSMModeSecondPage() //显示台号选择页面
          // this.EAMandSMModeSecondPage = true
          //EAMandSMAutoLogin为EnhAssistMode和StaffMode这两个单词合并简写,都是用于跳转到手动点击台号的页面跳转
          let loginObj = ""
          let { password, staffCode } = this.loginParams
          if (this.isStaffModeOne) {
            loginObj = JSON.stringify({
              password,
              staffCode,
              model: tableNumber
            })
          } else {
            loginObj = JSON.stringify({
              password,
              model: tableNumber
            })
          }
          sessionStorage.setItem("EAMandSMAutoLogin", loginObj)
        }
      }
    },
    /*
     *  员工模式下开台成功储存密码cookie
     * 注意:北京时间转换成GMT/UTC时间有8个小时的时间差
     */
    setStaffPassword(tableNumber, staffPassword) {
      if (["StaffMode", "AssistMode", "EnhAssistMode"].includes(tableNumber)) {
        let remPassWord = $("#remPassWord").is(":checked") // 复选框值
        let cookiesPassword = Cookies.get("staffPassword")
        // 删除cookie (未勾选记住密码)
        if (!remPassWord) {
          if (cookiesPassword) Cookies.remove("staffPassword")
        } else {
          if (!cookiesPassword || staffPassword !== cookiesPassword) {
            /*满足勾选记住密码
                                      没有cookie缓存/原缓存与现在密码不相同
                                    */
            // 设置当天零点
            // console.log(moment.utc('2021-12-15T04:03:01.000Z')._d, 'moment')
            // let zeroTime = moment().millisecond(0).second(0).minute(0).hour(0)
            // zeroTime = moment.utc(zeroTime)._d
            // console.log(zeroTime, 'zeroTime')

            // 东八区加了8小时
            // let millisecond = moment().valueOf()
            // let minute = 1 // 1440分钟为一天
            // let expiresTime = moment
            //   .utc(millisecond + 60 * 1000 * minute)
            //   .utcOffset(8)._d
            // console.log(expiresTime, 'expiresTime')
            // Cookies.set('staffPassword', staffPassword, {
            //   expires: expiresTime
            //   // expires: 7
            // }) //7天过期

            let millisecond = moment().valueOf()
            let minute = 1440 // 1440分钟为一天
            let expiresTime = moment.utc(millisecond + 60 * 1000 * minute)._d

            Cookies.set("staffPassword", staffPassword, {
              expires: expiresTime
              // expires: 7
            }) //7天过期
          }
        }
      }
    },
    // 下拉菜单切换语言
    chooseLan(item) {
      this.fixLan(item.val)
      if (this.openTableData) {
        this.openTableData = {
          ...this.openTableData,
          language: item.val
        }
      }
      layer.title(this.arrLang.staffModelLoginTitle, this.userModelLayerIndex) //重置弹窗标题
      if (this.initTableNumber === "AssistMode") {
        layer.title(this.arrLang.openTableDiaTitle, this.openTableLayerIndex) //重置弹窗标题
      }
      //判断是否存在限制浏览器的tip,有则动态更改
      this.changeBrowserLayerTip()
    },
    //关闭灰色 jQuery 遮罩
    closeBg() {
      $("#fullbg").hide()
      $("#dialog").hide({
        duration: 200,
        complete: () => {
          // 清除倒计时定时器
          if (this.popupCloseControl.countdownTimer) {
            clearInterval(this.popupCloseControl.countdownTimer)
            this.popupCloseControl.countdownTimer = null
          }

          // 清除视频验证定时器
          if (this.popupMedia.videoValidationTimer) {
            clearTimeout(this.popupMedia.videoValidationTimer)
          }

          // 重置弹窗媒体状态
          this.popupMedia = {
            url: "",
            type: "",
            hasExternalUrl: false,
            isLoading: false,
            videoValidationTimer: null,
            isVideoValid: false
          }

          // 重置关闭控制状态
          this.popupCloseControl = {
            isCloseBtnVisible: true,
            countdown: 0,
            countdownTimer: null
          }

          // 重置关闭区域可见状态
          this.isPopupCloseAreaVisible = false

          // 409状态下接入fixQRAutoOpenTable功能,正常模式下消费者填写人数自己开台
          if (this.errorOTRequestData && this.errorOTRequestData.mode == "fixQRAutoOpenTable")
            this.showOpenTableDia()
        }
      })
      this.isDisabledBtn = false
    },
    //改变第二模板的address-table文字位置 使其居中在center区域内
    resetDOMPosition() {
      if (this.template === 2) {
        let body_rect = document.body.getBoundingClientRect()
        let targetDOM = document.querySelector(".content_warp")
        let target_rect = targetDOM.getBoundingClientRect()
        let target_h = targetDOM.clientHeight
        let target_top = target_rect.top - body_rect.top //target 相对于 body 的top
        let centerDOM = document.querySelector(".center-info-warp")
        let center_h = centerDOM.clientHeight
        let centerRect = centerDOM.getBoundingClientRect()
        let center_top = centerRect.top - body_rect.top //center 相对于 body 的top
        //center高度大于 target 高度 则居中平移
        if (center_h > target_h) {
          let h_diff = parseInt(center_h - target_h) / 2
          let top_diff = parseInt(target_top - center_top)
          let translation_distance = h_diff - top_diff

          targetDOM.style.transform = `translateY(${translation_distance || 0}px)`
        } else {
          //小于则 将center区域高度改为target高度,在平移到center中置顶
          let top_diff = parseInt(center_top - target_top)
          targetDOM.style.transform = `translateY(${top_diff || 0}px)`
        }
      }
    },
    //開始下單點擊事件
    async goNextPageBtn() {
      if (this.resErrorCode) {
        this.opTableRequestTip(this.resErrorCode)
      } else {
        if (this.initTableNumber === "lbsMode" && !this.openTableStatus) {
          this.localTableNumber ? this.openTable(this.localTableNumber) : this.showUserModelDia()
          return
        }
        if (this.showCRM) {
          await this.checkInviteLogin().catch()
        }
        layer.closeAll("loading")
        $("#shade").show()
        $(".orders_btn").css({ visibility: "hidden" })
        $("#loading").show()
        sessionStorage.setItem("openTable", JSON.stringify(this.openTableData))
        sessionStorage.setItem("indexPageUrl", window.location.href)
        if (this.openTableData.page === 1) {
          if (
            this.initTableNumber === "TAKEAWAY" &&
            this.storeNumber === "*" &&
            !this.urlParams.paramsObj.mode
          ) {
            window.location.href = "./order/map.html"
          } else {
            if (this.urlParams.paramsObj.mode === "FoodCourt" && this.storeNumber === "*") {
              if (!this.openTableData.foodCourt) {
                layer.msg(this.arrLang.missFoodCourtError)
                $("#shade").hide()
                $(".orders_btn").css({ visibility: "visible" })
                $("#loading").hide()
                return false
              }
              let params = {
                mode: "FoodCourt",
                config: {
                  color: this.openTableData.color,
                  ...this.openTableData.foodCourt
                }
              }
              sessionStorage.setItem("mode", JSON.stringify(params))
            }
            window.location.href = "./order/menuPage"
            // window.location.href = "./order/paySuccessPage.html"
          }
        }
        // else {
        //   // 新版
        //   window.location.href = `./order/newMenuPage`
        // }
      }
    },
    //禁用瀏覽器的前進跟後退按鈕
    forbidden() {
      if (window.history && window.history.pushState) {
        $(window).on("popstate", function () {
          /// 当点击浏览器的 后退和前进按钮 时才会被触发，
          window.history.pushState("forward", null, "")
          window.history.forward(1)
        })
      }
      //
      // window.history.pushState('forward', null, '');  //在IE中必须得有这两行
      // window.history.forward(1);

      if ("pushState" in history) {
        window.history.pushState("forward", null, "#")
        window.history.forward(1)
      } else {
        History.pushState("forward", null, "?state=2")
        window.history.forward(1)
      }
      window.onhashchange = function () {
        History.pushState("forward", null, "?state=1")
      }
    },

    async initSpecialMode() {
      let EAMandSMAutoLogin = sessionStorage.getItem("EAMandSMAutoLogin")
      const clearStorage = () => {
        localStorage.clear()
        localStorage.setItem("indexPageUrl", window.location.href)
      }
      if (EAMandSMAutoLogin) {
        //解析EAMandSMAutoLogin 重新赋值已登录状态数据
        let loginObj = JSON.parse(EAMandSMAutoLogin)
        if (loginObj.model == this.initTableNumber) {
          //重新获取配置数据
          await this.jsonData(localStorage.getItem("language"))
          this.showEAMandSMModeSecondPage() //显示台号选择页面
          if (this.isStaffModeOne) {
            this.loginParams.password = loginObj.password
            this.loginParams.staffCode = loginObj.staffCode
          } else {
            this.loginParams.password = loginObj.password
          }
          this.openTable("", this.loginParams.password)
        } else {
          //模式对不上,调用登出逻辑(避免登录后重新切换别的模式共用EAMandSMAutoLogin)
          this.modeLogout()
        }
      } else {
        //  没有参数
        clearStorage()
      }
    },
    //特殊 mode 登出
    modeLogout() {
      this.initBGImg = false //  重置背景图动画状态
      if (["StaffMode", "EnhAssistMode", "AssistMode"].includes(this.initTableNumber)) {
        // 增强助理模式
        this.loginParams.staffCode = ""
        this.loginParams.password = ""
        this.EAMandSMModeSecondPage = false
        sessionStorage.removeItem("EAMandSMAutoLogin")
        this.render()
      } else {
        this.loginParams.tableNumber = ""
        this.render()
      }
      localStorage.clear()
    },

    tableNumberInputBlur() {
      this.readonly = true
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          behavior: "smooth"
        })
      }, 100)
    },
    // 广告图链接跳转
    advertisingMapJump(type) {
      let url = ""
      if (type == "PopupImage") {
        url = this.advertisingLink
      } else if (type == "PopupThumbnail") {
        // 广告缩略图自身没有定义,则去获取广告图定义的link
        url = this.adLinkPThumbnail || this.advertisingLink
      }
      url = this.popupParseUrl(url)
      if (url) {
        if (url.indexOf("http") == -1) {
          url = "http://" + url
        }
        window.open(url)
      }
    },
    popupParseUrl(url) {
      // 检查URL是否包含参数
      if (!url.includes("?")) {
        return url // 如果不包含参数，直接返回原URL
      }

      // 分离基础URL和查询参数
      const [baseUrl, queryString] = url.split("?")

      // 如果没有查询参数，直接返回
      if (!queryString) {
        return url
      }

      const replacementMap = {
        "#lan": function () {
          return this.openTableData.language
        },
        "#store": function () {
          return this.storeNumber
        }
      }
      // 解析查询参数
      const params = new URLSearchParams(queryString)
      const newParams = new URLSearchParams()

      // 遍历所有参数
      for (const [key, value] of params.entries()) {
        // 检查参数值是否以'#'开头
        if (value.startsWith("#")) {
          // 检查是否有对应的替换规则
          if (replacementMap[value]) {
            // 使用替换规则获取实际值
            const actualValue = replacementMap[value].call(this)
            newParams.append(key, actualValue)
          } else {
            newParams.set(key, value) // 没有找到替换规则，保留原值(#会自动编码为%23)
          }
        } else {
          // 不需要替换的参数，保留原值
          newParams.append(key, value)
        }
      }

      // 构建新的URL
      const newQueryString = newParams.toString()
      return `${baseUrl}?${newQueryString}`
    },
    // 动态设置右下角div盒子宽度
    setByRightInfoWidth() {
      if (!this.arrLang.poweredByMessage) {
        $(".byRightInfo").css("width", `115px`)
      } else {
        // 创建一个隐藏的 canvas 元素
        let canvas = document.createElement("canvas")
        let context = canvas.getContext("2d")
        // 设置字体样式
        context.font = "12px sans-serif" // 设置为与 p 标签相同的字体
        // 测量文本的宽度
        let text = this.arrLang.poweredByMessage
        let width = context.measureText(text).width
        // 应用宽度到相应的元素
        $(".byRightInfo").css("width", `${width}px`)
        // 销毁 canvas 元素
        canvas = null
      }
    },
    tableClick(table) {
      //有存在table.tableKey代表已经开台了,带着台号台key直接进下一页,否则进入开台页面
      if (table.tableKey) {
        this.openTableData.tableNumber = table.tableNumber // 重置tableNumber
        this.openTableData.tableKey = table.tableKey //保存tableKey
        this.openTableData.orderStartTime = table.orderStartTime // 开台时间
        layer.load(2) // 持续loading,解决开台成功后,页面跳转前loading消失的问题
        this.goNextPageBtn()
      } else {
        this.loginParams.tableNumber = table.tableNumber //重新赋值台号,解决showOpenTableDia()中的tableNumber不是最新的问题
        let pax =
          this.initTableNumber == "StaffMode"
            ? Number(sessionStorage.getItem("staffModePax"))
            : Number(sessionStorage.getItem("assistModePax"))
        if (pax > 0) {
          this.openTable(table.tableNumber, this.loginParams.password, pax)
        } else {
          this.isShowOpenTableDia() //EnhAssistMode模式弹出填写人数窗口
        }
      }
    },
    //判断当前是否是首页
    checkPageAndSetPassword() {
      let isIndexPage = document.getElementById("section-home")
      let needSetPassword = isIndexPage || this.initTableNumber == "AssistMode"
      return { isIndexPage, needSetPassword }
    },
    mountedClearSessionStorage() {
      // 保留部分数据避免删除
      let keepData = ["i18nPkg", "useBackupOss", "oldLanguage", "showBirthdayPrompt"]
      switch (this.initTableNumber) {
        case "StaffMode":
          keepData = [...keepData, "EAMandSMAutoLogin", "staffModePax"]
          break
        case "AssistMode":
        case "EnhAssistMode":
          keepData = [...keepData, "EAMandSMAutoLogin", "assistModePax"]
          break
        default:
          break
      }
      let oldOpenTable = sessionStorage.getItem("openTable")
      let oldLanguage = oldOpenTable
        ? JSON.parse(oldOpenTable).language || ""
        : sessionStorage.getItem("oldLanguage") || ""
      sessionStorage.setItem("oldLanguage", oldLanguage)
      let keys = Object.keys(sessionStorage)
      keys.forEach(key => {
        if (!keepData.includes(key)) {
          sessionStorage.removeItem(key)
        }
      })
    },
    //设置performType
    getPerformType() {
      const { paramsObj } = urlResolve()
      const { salesmode } = paramsObj || ""
      const tableNumForMD = ["StaffMode", "lbsMode", "AssistMode", "EnhAssistMode", "PREORDER"]
      const tableNumForFD = ["TAKEAWAY"]
      let performType = 1
      if (salesmode) {
        // 将salesmode的值全部转化为小写
        performType = salesmode.toLowerCase() == "fb" ? 2 : 1
      } else {
        const isMDTable = tableNumForMD.includes(this.initTableNumber)
        const isFDTable = tableNumForFD.includes(this.initTableNumber)
        performType = isMDTable ? 1 : isFDTable ? 2 : 1
      }
      return performType
    },
    //合并后的staffMode或者原始EnhAssistMode的手动输入台号开台
    onJumpTableNum() {
      //用this.jumpTableNum去this.tables里面find出tableNumber相同的对象
      let table = this.tables.find(el => el.tableNumber == this.jumpTableNum)
      if (table) {
        this.tableClick(table)
      } else {
        layer.msg(this.arrLang.invalidTableNumTip)
      }
    },
    setStaffAssModeSession() {
      return new Promise(async (resolve, reject) => {
        let typeNameList = this.initTableNumber === "StaffMode" ? ["StaffMode"] : ["assistMode"]
        const findUiConfigByType = (uiConfigList, type) => {
          return uiConfigList.find(el => el.type === type)
        }
        let res = await this.getSomeUIConfig(typeNameList)
        if (this.initTableNumber === "StaffMode") {
          let staffModeUiConfig = findUiConfigByType(res.data, "StaffMode")
          if (staffModeUiConfig) {
            this.staffModeUIConfig = JSON.parse(staffModeUiConfig.value)
            sessionStorage.setItem("staffModePax", this.staffModeUIConfig.pax)
          }
        } else if (
          this.initTableNumber == "AssistMode" ||
          this.initTableNumber == "EnhAssistMode"
        ) {
          let assistModeUiConfig = findUiConfigByType(res.data, "assistMode")
          if (assistModeUiConfig) {
            this.assistModeUIConfig = JSON.parse(assistModeUiConfig.value)
            sessionStorage.setItem("assistModePax", this.assistModeUIConfig.pax)
          }
        }
        resolve(true)
      })
    },
    async showEAMandSMModeSecondPage() {
      await this.setStaffAssModeSession()
      this.EAMandSMModeSecondPage = true
    },
    getSomeUIConfig(typeNameList) {
      return new Promise((resolve, reject) => {
        // 获取UIConfig
        let requestLoading = layer.load(2)
        let { companyName } = this.urlParams
        let errorConfigTip = "配置获取失败"
        let data = {
          domain: companyName,
          storeNumber: this.storeNumber,
          typeNameList
        }
        $.ajax({
          type: "post",
          url: "./uiConfig/getSpecifiedUIConfig",
          dataType: "json",
          traditional: true,
          data,
          success: res => {
            res.statusCode == 200 ? resolve(res) : reject(errorConfigTip)
          },
          error: err => {
            reject(errorConfigTip)
          },
          complete: () => {
            layer.close(requestLoading)
          }
        })
      })
    },
    //初始化时候检测是否放行加载(部分设备可配置禁止加载页面)
    checkDevice() {
      let { forceSalesMode = "", browserRestrictions: { category = [] } = {} } = this.initConfigObj
      let isForcedSalesMode = forceSalesMode || this.getPerformType()
      // 只有FB模式才会检测设备
      if (isForcedSalesMode != 2) return
      let { brRestrictTip } = this.arrLang
      let device = navigator.userAgent
      let isAndroid = device.indexOf("Android") > -1 || device.indexOf("Adr") > -1 //android终端
      let isAlipay = /Alipay/i.test(device) //支付宝
      let isWeChat = /MicroMessenger/i.test(device) //微信
      const layerMsg = msg => {
        layer.msg(msg, {
          shade: [0],
          time: 0,
          anim: 6,
          // zIndex: 1,
          area: "86%",
          success: function (layero, index) {
            $(layero).addClass("browserTip")
            //改变语言栏的Z-index
            $(".ftco-cover .new_inSwitch").css("z-index", 20240118)
            $("#dropdown-standard").css("z-index", 20240118)
          }
        })
      }
      if ((isAlipay && category.includes("AliPay")) || (isWeChat && category.includes("WeChat"))) {
        this.resErrorCode = "不支持此浏览器环境"
        layerMsg(brRestrictTip)
      }
    },
    changeBrowserLayerTip() {
      // 获取 layer.msg 创建的元素
      let layerContentElement = document.querySelector(".browserTip .layui-layer-content")
      let { brRestrictTip } = this.arrLang
      if (layerContentElement) {
        // 修改元素的文本内容
        layerContentElement.textContent = brRestrictTip
      }
    },
    setConfigCallBack() {
      //设置PC端的背景主题颜色
      let { pcBackgroundColor } = this.initConfigObj
      if (this.isPcDevices && pcBackgroundColor) {
        sessionStorage.setItem("PCbgColor", pcBackgroundColor)
        $("html").css({ "background-color": pcBackgroundColor })
      }
    },
    isOrderWaiting() {
      let { orderWaitTimeTip, btnTxtForCancel, btnTxtForConfirm } = this.orderWaitText
      layer.confirm(
        orderWaitTimeTip,
        {
          shade: [0.1, "#fff"],
          closeBtn: 0,
          title: false,
          btn: [btnTxtForConfirm], //按钮
          success: (layero, index) => {
            //改变语言栏的Z-index
            $(".ftco-cover .new_inSwitch").css("z-index", 20240118)
            $("#dropdown-standard").css("z-index", 20240118)
          }
        },
        (index, layero) => {
          // 取消
          layer.close(index)
        },
        (index, layero) => {}
      )
      //判断点餐是否需要等待,或者是否爆仓暂停点餐
      // let { isWaitOrder, isStopOrder } = this.initConfigObj
      // showOrderWait()
    },
    // 是否存在cookie信息,若在computed中會不同步
    LoginValid() {
      return Cookies.get("userLogged") !== undefined
    }
  },

  async created() {
    this.initSpecialMode().then() // 初始化各种特殊模式(自动登录,staffMode/EnhAssistMode)
    this.forbidden()
    this.initConfig()
  },
  async mounted() {
    this.mountedClearSessionStorage() // 避免全部删除
    if (!sessionStorage.getItem("EAMandSMAutoLogin")) {
      await this.render()
      checkOrderWait(this.urlParams.companyName, this.storeNumber, "index", true) //判断是否需要暂停下单
    }
  },

  computed: {
    showCRM() {
      const { displayCRM, memberConfig } = this.openTableData
      if (!displayCRM) return false
      const { dineInMemberLoginSwitch = true, takeawayMemberLoginSwitch = true } = displayCRM
      let isTakeAway = this.initTableNumber == "TAKEAWAY"
      if (isTakeAway) return takeawayMemberLoginSwitch
      return dineInMemberLoginSwitch
    },
    orderWaitText() {
      return {
        orderWaitTimeTip: this.arrLang.orderWaitTimeTip,
        btnTxtForCancel: this.arrLang.btnTxtForCancel,
        btnTxtForConfirm: this.arrLang.btnTxtForConfirm
      }
    },
    tableNumber() {
      return urlResolve().paramsObj && urlResolve().paramsObj.tableNumber
    },
    //显示首页地址
    showShopAddress() {
      if (this.openTableData.address) {
        return this.openTableData.address[this.currentLan]
      } else {
        return ""
      }
    },
    //显示首页tableNumber
    showShopTableNumber() {
      if (this.showDiningStyle) {
        return this.arrLang.dineInPreTableNumTxt
      } else if (this.openTableData.dineInStoreNumber) {
        let { dineInStoreNumber } = this.openTableData
        let include = dineInStoreNumber
          .toString()
          .split(";")
          .filter(e => e)
        if (include.includes(this.initTableNumber)) {
          return this.arrLang.dineIn
        } else {
          //获取原有的值显示
          let oldValue = $(".byRightInfo_tableNUm_txt").text() //用于有dineInStoreNumber配置但不在其中的情况下显示原有的值
          return oldValue
        }
      } else {
        switch (this.initTableNumber) {
          case "TAKEAWAY":
            return this.arrLang.pickupTakes || ""
          case "PREORDER":
            return this.arrLang.preOrderTitle || ""
          case "lbsMode":
            return `${this.arrLang.tableNumberLabel || ""}${
              this.localTableNumber || this.initTableNumber || ""
            }`
          case "StaffMode":
            return `${this.arrLang.tableNumberLabel || ""}${this.loginParams.tableNumber || ""}`
          default:
            return `${this.arrLang.tableNumberLabel || ""}${this.initTableNumber || ""}`
        }
      }
    },
    getTimeOutPrompt() {
      // 获取超时提示语
      if (!this.errorOTRequestData) return
      let isTakeaway = this.initTableNumber == "TAKEAWAY"
      let { takeawayTimeoutPrompt, takeawayClosePrompt, dineInTimeoutPrompts, dineInPrompts } =
        this.arrLang
      let { errorDesc = {} } = this.errorOTRequestData
      let useTip = ""
      switch (this.resErrorCode) {
        case 4006:
          useTip = isTakeaway
            ? errorDesc[this.currentLan] || takeawayTimeoutPrompt
            : errorDesc[this.currentLan] || dineInTimeoutPrompts
          break
        case 4011:
          useTip = isTakeaway ? takeawayClosePrompt : dineInPrompts
          break
      }
      return useTip

      // let { errorDesc = {} } = this.errorOTRequestData
      // if (errorDesc) {
      //   return errorDesc[this.currentLan]
      // } else {
      //   return ""
      // }
    },
    isStaffModeOne() {
      //是否为员工模式第一种登录模式(账号+密码)
      let loginModel = 1
      return this.initTableNumber == "StaffMode" && loginModel == 1
    },
    isPcDevices() {
      return !isMobile() && os.isPc
    }
  },
  watch: {
    EAMandSMModeSecondPage(val) {
      if (val) {
        this.jumpTableNum = "" // 重置手动输入台号
      }
    },
    currentLan(val) {
      this.setByRightInfoWidth()
    }
  }
})
